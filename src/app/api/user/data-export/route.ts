import { NextRequest, NextResponse } from 'next/server'
import { getCurrentUser } from '@/lib/auth'
import { query } from '@/lib/local-db'

export async function GET(request: NextRequest) {
  try {
    const user = await getCurrentUser()
    
    if (!user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Collect all user data from various tables
    const userData = {
      user: {
        id: user.id,
        email: user.email,
        firstName: user.firstName,
        lastName: user.lastName,
        emailVerified: user.emailVerified,
        role: user.role,
        paymentStatus: user.paymentStatus,
        companyId: user.companyId,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      },
      exportInfo: {
        exportDate: new Date().toISOString(),
        exportVersion: '1.0',
        dataTypes: []
      }
    }

    // Get saved companies
    try {
      const savedCompaniesResult = await query(`
        SELECT sc.*, c.name as company_name, c.domain, c.industry
        FROM saved_companies sc
        JOIN companies c ON sc.company_id = c.id
        WHERE sc.user_id = $1
        ORDER BY sc.created_at DESC
      `, [user.id])
      
      userData.exportInfo.dataTypes.push('saved_companies')
      userData.savedCompanies = savedCompaniesResult.rows
    } catch (error) {
      console.error('Error fetching saved companies:', error)
    }

    // Get benefit verifications
    try {
      const verificationsResult = await query(`
        SELECT bv.*, cb.company_id, c.name as company_name, b.name as benefit_name
        FROM benefit_verifications bv
        JOIN company_benefits cb ON bv.company_benefit_id = cb.id
        JOIN companies c ON cb.company_id = c.id
        JOIN benefits b ON cb.benefit_id = b.id
        WHERE bv.user_id = $1
        ORDER BY bv.created_at DESC
      `, [user.id])
      
      userData.exportInfo.dataTypes.push('benefit_verifications')
      userData.benefitVerifications = verificationsResult.rows
    } catch (error) {
      console.error('Error fetching benefit verifications:', error)
    }

    // Get benefit disputes
    try {
      const disputesResult = await query(`
        SELECT brd.*, cb.company_id, c.name as company_name, b.name as benefit_name
        FROM benefit_removal_disputes brd
        JOIN company_benefits cb ON brd.company_benefit_id = cb.id
        JOIN companies c ON cb.company_id = c.id
        JOIN benefits b ON cb.benefit_id = b.id
        WHERE brd.user_id = $1
        ORDER BY brd.created_at DESC
      `, [user.id])
      
      userData.exportInfo.dataTypes.push('benefit_disputes')
      userData.benefitDisputes = disputesResult.rows
    } catch (error) {
      console.error('Error fetching benefit disputes:', error)
    }

    // Get analytics data (anonymized)
    try {
      const analyticsResult = await query(`
        SELECT 
          'company_view' as event_type,
          company_id,
          created_at,
          'anonymized' as details
        FROM company_page_views
        WHERE user_id = $1
        UNION ALL
        SELECT 
          'search' as event_type,
          NULL as company_id,
          created_at,
          'anonymized' as details
        FROM search_queries
        WHERE user_id = $1
        UNION ALL
        SELECT 
          'benefit_interaction' as event_type,
          company_id,
          created_at,
          interaction_type as details
        FROM benefit_search_interactions
        WHERE user_id = $1
        ORDER BY created_at DESC
        LIMIT 1000
      `, [user.id])
      
      userData.exportInfo.dataTypes.push('analytics_summary')
      userData.analyticsSummary = {
        totalEvents: analyticsResult.rows.length,
        events: analyticsResult.rows
      }
    } catch (error) {
      console.error('Error fetching analytics data:', error)
    }

    // Get user rankings if they exist
    try {
      const rankingsResult = await query(`
        SELECT ur.*, b.name as benefit_name
        FROM user_benefit_rankings ur
        JOIN benefits b ON ur.benefit_id = b.id
        WHERE ur.user_id = $1
        ORDER BY ur.ranking ASC
      `, [user.id])
      
      if (rankingsResult.rows.length > 0) {
        userData.exportInfo.dataTypes.push('benefit_rankings')
        userData.benefitRankings = rankingsResult.rows
      }
    } catch (error) {
      console.error('Error fetching user rankings:', error)
    }

    // Get auth logs (last 100 entries)
    try {
      const authLogsResult = await query(`
        SELECT event_type, status, created_at, ip_address
        FROM auth_logs
        WHERE email = $1
        ORDER BY created_at DESC
        LIMIT 100
      `, [user.email])
      
      userData.exportInfo.dataTypes.push('auth_logs')
      userData.authLogs = authLogsResult.rows
    } catch (error) {
      console.error('Error fetching auth logs:', error)
    }

    // Add privacy notice
    userData.privacyNotice = {
      message: "This export contains all personal data we have stored about you in accordance with GDPR Article 15 (Right of Access).",
      dataRetention: "Most data is retained for the duration of your account. Analytics data may be anonymized after 2 years.",
      contact: "For questions about your data, contact <EMAIL>",
      rights: [
        "Right to rectification (Article 16)",
        "Right to erasure (Article 17)",
        "Right to restrict processing (Article 18)",
        "Right to data portability (Article 20)",
        "Right to object (Article 21)"
      ]
    }

    // Set headers for file download
    const filename = `benefitlens-data-export-${user.id}-${new Date().toISOString().split('T')[0]}.json`
    
    return new NextResponse(JSON.stringify(userData, null, 2), {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Content-Disposition': `attachment; filename="${filename}"`,
        'Cache-Control': 'no-cache, no-store, must-revalidate'
      }
    })

  } catch (error) {
    console.error('Error exporting user data:', error)
    return NextResponse.json(
      { error: 'Failed to export user data' },
      { status: 500 }
    )
  }
}
