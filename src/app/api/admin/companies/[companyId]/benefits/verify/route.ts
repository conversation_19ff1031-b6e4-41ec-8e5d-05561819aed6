import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/auth'
import { query } from '@/lib/local-db'
import { invalidateCompanyCache } from '@/lib/postgresql-cache'

// POST /api/admin/companies/[companyId]/benefits/verify - Bulk verify/unverify company benefits
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    await requireAdmin()
    const { companyId } = await params
    const body = await request.json()
    const { action, benefitIds, reason } = body

    if (!action || !['verify', 'unverify'].includes(action)) {
      return NextResponse.json(
        { error: 'Action must be "verify" or "unverify"' },
        { status: 400 }
      )
    }

    if (!benefitIds || !Array.isArray(benefitIds) || benefitIds.length === 0) {
      return NextResponse.json(
        { error: 'Benefit IDs array is required' },
        { status: 400 }
      )
    }

    // Check if company exists
    const companyResult = await query(
      'SELECT id, name FROM companies WHERE id = $1',
      [companyId]
    )

    if (companyResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Company not found' },
        { status: 404 }
      )
    }

    const company = companyResult.rows[0]
    const isVerified = action === 'verify'

    // Get current company benefits that match the provided benefit IDs
    const placeholders = benefitIds.map((_, i) => `$${i + 2}`).join(',')
    const currentBenefitsResult = await query(
      `SELECT cb.id as company_benefit_id, cb.benefit_id, b.name as benefit_name
       FROM company_benefits cb
       JOIN benefits b ON cb.benefit_id = b.id
       WHERE cb.company_id = $1 AND cb.benefit_id IN (${placeholders})`,
      [companyId, ...benefitIds]
    )

    if (currentBenefitsResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'No matching company benefits found' },
        { status: 404 }
      )
    }

    const foundBenefits = currentBenefitsResult.rows
    const foundBenefitIds = foundBenefits.map(b => b.benefit_id)
    const notFoundBenefitIds = benefitIds.filter(id => !foundBenefitIds.includes(id))

    // Update verification status for found benefits
    const updatePlaceholders = foundBenefitIds.map((_, i) => `$${i + 3}`).join(',')
    await query(
      `UPDATE company_benefits
       SET is_verified = $1
       WHERE company_id = $2 AND benefit_id IN (${updatePlaceholders})`,
      [isVerified, companyId, ...foundBenefitIds]
    )

    // Invalidate company cache to ensure public page shows updated verification status
    await invalidateCompanyCache(companyId)

    // Log the admin action (optional - you could add an admin_actions table)
    const benefitNames = foundBenefits.map(b => b.benefit_name)
    
    const response = {
      success: true,
      message: `${action === 'verify' ? 'Verified' : 'Unverified'} ${foundBenefits.length} benefit(s) for ${company.name}`,
      details: {
        company: company.name,
        action,
        updatedBenefits: benefitNames,
        updatedCount: foundBenefits.length,
        ...(notFoundBenefitIds.length > 0 && {
          notFound: notFoundBenefitIds,
          notFoundCount: notFoundBenefitIds.length
        }),
        ...(reason && { reason })
      }
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Error bulk verifying company benefits:', error)
    return NextResponse.json(
      { error: 'Failed to update benefit verification status' },
      { status: 500 }
    )
  }
}

// PATCH /api/admin/companies/[companyId]/benefits/verify - Update single benefit verification
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ companyId: string }> }
) {
  try {
    await requireAdmin()
    const { companyId } = await params
    const body = await request.json()
    const { companyBenefitId, isVerified, reason } = body

    if (!companyBenefitId) {
      return NextResponse.json(
        { error: 'Company benefit ID is required' },
        { status: 400 }
      )
    }

    if (typeof isVerified !== 'boolean') {
      return NextResponse.json(
        { error: 'isVerified must be a boolean' },
        { status: 400 }
      )
    }

    // Get company benefit info
    const benefitResult = await query(`
      SELECT 
        cb.id,
        cb.is_verified as current_status,
        c.name as company_name,
        b.name as benefit_name
      FROM company_benefits cb
      JOIN companies c ON cb.company_id = c.id
      JOIN benefits b ON cb.benefit_id = b.id
      WHERE cb.id = $1 AND cb.company_id = $2
    `, [companyBenefitId, companyId])

    if (benefitResult.rows.length === 0) {
      return NextResponse.json(
        { error: 'Company benefit not found' },
        { status: 404 }
      )
    }

    const benefit = benefitResult.rows[0]

    // Update verification status
    await query(
      'UPDATE company_benefits SET is_verified = $1 WHERE id = $2',
      [isVerified, companyBenefitId]
    )

    // Invalidate company cache to ensure public page shows updated verification status
    await invalidateCompanyCache(companyId)

    return NextResponse.json({
      success: true,
      message: `${isVerified ? 'Verified' : 'Unverified'} "${benefit.benefit_name}" for ${benefit.company_name}`,
      details: {
        companyBenefitId,
        benefitName: benefit.benefit_name,
        companyName: benefit.company_name,
        previousStatus: benefit.current_status,
        newStatus: isVerified,
        ...(reason && { reason })
      }
    })

  } catch (error) {
    console.error('Error updating benefit verification:', error)
    return NextResponse.json(
      { error: 'Failed to update benefit verification' },
      { status: 500 }
    )
  }
}
