import { NextRequest, NextResponse } from 'next/server'
import { requireAdmin } from '@/lib/auth'
import { clearAllCache, invalidateCompanyCache } from '@/lib/postgresql-cache'

export async function POST(request: NextRequest) {
  try {
    await requireAdmin()
    
    const body = await request.json()
    const { type, companyId } = body
    
    if (type === 'all') {
      await clearAllCache()
      return NextResponse.json({
        success: true,
        message: 'All cache cleared successfully'
      })
    } else if (type === 'company' && companyId) {
      await invalidateCompanyCache(companyId)
      return NextResponse.json({
        success: true,
        message: `Cache cleared for company ${companyId}`
      })
    } else {
      return NextResponse.json(
        { error: 'Invalid cache clear type or missing companyId' },
        { status: 400 }
      )
    }
    
  } catch (error) {
    console.error('Error clearing cache:', error)
    return NextResponse.json(
      { error: 'Failed to clear cache' },
      { status: 500 }
    )
  }
}
