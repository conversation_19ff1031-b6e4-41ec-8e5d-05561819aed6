import type { Metadata, Viewport } from "next";
import "./globals.css";
import { Footer } from "@/components/footer";
import { EnhancedCookieBanner } from "@/components/enhanced-cookie-banner";

// Initialize performance monitoring and environment validation
import { startPerformanceMonitoring } from "@/lib/performance";
import { validateEnvironment } from "@/lib/env-validation";

// Initialize systems on server startup (skip during build phase)
if (typeof window === 'undefined' && process.env.BUILD_PHASE !== 'true') {
  // Validate environment
  validateEnvironment();

  // Start performance monitoring
  startPerformanceMonitoring();
}

export const metadata: Metadata = {
  title: "BenefitLens - Compare Companies by Benefits",
  description: "Find and compare companies based on their employee benefits. Search by location, benefits, and more.",
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className="antialiased bg-gray-50">
        <div className="min-h-screen flex flex-col">
          <div className="flex-1">
            {children}
          </div>
          <Footer />
        </div>
        <EnhancedCookieBanner />
      </body>
    </html>
  );
}
