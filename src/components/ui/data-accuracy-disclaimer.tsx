import { AlertTriangle, Info } from 'lucide-react'

interface DataAccuracyDisclaimerProps {
  variant?: 'warning' | 'info'
  size?: 'sm' | 'md' | 'lg'
  className?: string
  showIcon?: boolean
}

export function DataAccuracyDisclaimer({ 
  variant = 'warning', 
  size = 'md',
  className = '',
  showIcon = true 
}: DataAccuracyDisclaimerProps) {
  const baseClasses = "rounded-lg border-l-4 p-3"
  
  const variantClasses = {
    warning: "bg-yellow-50 border-yellow-400 text-yellow-800",
    info: "bg-blue-50 border-blue-400 text-blue-800"
  }
  
  const sizeClasses = {
    sm: "text-xs",
    md: "text-sm", 
    lg: "text-base"
  }
  
  const iconSizes = {
    sm: "w-3 h-3",
    md: "w-4 h-4",
    lg: "w-5 h-5"
  }

  const Icon = variant === 'warning' ? AlertTriangle : Info

  return (
    <div className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`}>
      <div className="flex items-start gap-2">
        {showIcon && (
          <Icon className={`${iconSizes[size]} flex-shrink-0 mt-0.5`} />
        )}
        <div className="flex-1">
          <p className="font-medium mb-1">
            Wichtiger Hinweis zur Datengenauigkeit
          </p>
          <p className="leading-relaxed">
            Alle Leistungsinformationen werden ohne Gewähr bereitgestellt und können sich jederzeit ändern. 
            Bitte überprüfen Sie alle Angaben direkt beim jeweiligen Unternehmen, bevor Sie wichtige 
            Entscheidungen treffen.
          </p>
        </div>
      </div>
    </div>
  )
}

interface BenefitVerificationDisclaimerProps {
  className?: string
}

export function BenefitVerificationDisclaimer({ className = '' }: BenefitVerificationDisclaimerProps) {
  return (
    <div className={`bg-gray-50 border border-gray-200 rounded-lg p-3 ${className}`}>
      <div className="flex items-start gap-2">
        <Info className="w-4 h-4 text-gray-600 flex-shrink-0 mt-0.5" />
        <div className="text-sm text-gray-700">
          <p className="font-medium mb-1">Über Benefit-Verifizierung</p>
          <ul className="space-y-1 text-xs">
            <li>• <strong>Verifiziert:</strong> Von Mitarbeitern oder Unternehmen bestätigt</li>
            <li>• <strong>Nicht verifiziert:</strong> Noch nicht bestätigt oder umstritten</li>
            <li>• Verifizierungsstatus kann sich ändern</li>
            <li>• Immer beim Unternehmen direkt nachfragen</li>
          </ul>
        </div>
      </div>
    </div>
  )
}

interface CompanyDataDisclaimerProps {
  companyName: string
  className?: string
}

export function CompanyDataDisclaimer({ companyName, className = '' }: CompanyDataDisclaimerProps) {
  return (
    <div className={`bg-amber-50 border border-amber-200 rounded-lg p-4 ${className}`}>
      <div className="flex items-start gap-3">
        <AlertTriangle className="w-5 h-5 text-amber-600 flex-shrink-0 mt-0.5" />
        <div className="text-sm text-amber-800">
          <p className="font-medium mb-2">Haftungsausschluss für {companyName}</p>
          <div className="space-y-1">
            <p>• Alle angezeigten Leistungen sind ohne Gewähr</p>
            <p>• Informationen können veraltet oder unvollständig sein</p>
            <p>• Leistungen können sich ohne Vorankündigung ändern</p>
            <p>• Kontaktieren Sie {companyName} direkt für aktuelle Informationen</p>
          </div>
          <p className="mt-2 font-medium">
            BenefitLens übernimmt keine Haftung für Entscheidungen basierend auf diesen Informationen.
          </p>
        </div>
      </div>
    </div>
  )
}
