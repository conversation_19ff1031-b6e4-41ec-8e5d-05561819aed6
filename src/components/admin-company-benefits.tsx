'use client'

import { useState, useEffect, useCallback } from 'react'
import {
  CheckCircle,
  XCircle,
  Plus,
  Trash2,
  Shield,
  ShieldCheck,
  Search,
  AlertTriangle,
  Users,
  Eye
} from 'lucide-react'
import { Button } from '@/components/ui/button'

interface CompanyBenefit {
  company_benefit_id: string
  benefit_id: string
  benefit_name: string
  category: string
  icon?: string
  is_verified: boolean
  added_at: string
  confirmed_count: number
  disputed_count: number
  total_verifications: number
}

interface Benefit {
  id: string
  name: string
  category: string
  icon?: string
}

interface AdminCompanyBenefitsProps {
  companyId: string
  companyName: string
  onClose: () => void
}

export function AdminCompanyBenefits({ companyId, companyName, onClose }: AdminCompanyBenefitsProps) {
  const [companyBenefits, setCompanyBenefits] = useState<CompanyBenefit[]>([])
  const [allBenefits, setAllBenefits] = useState<Benefit[]>([])
  const [loading, setLoading] = useState(false)
  const [selectedBenefits, setSelectedBenefits] = useState<string[]>([])
  const [showAddModal, setShowAddModal] = useState(false)
  const [searchTerm, setSearchTerm] = useState('')
  const [categoryFilter, setCategoryFilter] = useState('all')

  const fetchCompanyBenefits = useCallback(async () => {
    setLoading(true)
    try {
      const response = await fetch(`/api/admin/companies/${companyId}/benefits`)
      if (response.ok) {
        const data = await response.json()
        setCompanyBenefits(data.benefits || [])
      } else {
        console.error('Error fetching company benefits - HTTP status:', response.status)
      }
    } catch (error) {
      console.error('Error fetching company benefits:', error)
    } finally {
      setLoading(false)
    }
  }, [companyId])

  const fetchAllBenefits = useCallback(async () => {
    try {
      const response = await fetch('/api/admin/benefits?limit=10000')
      if (response.ok) {
        const data = await response.json()
        setAllBenefits(data.benefits || [])
      } else {
        console.error('Error fetching all benefits - HTTP status:', response.status)
      }
    } catch (error) {
      console.error('Error fetching all benefits:', error)
    }
  }, [])

  useEffect(() => {
    fetchCompanyBenefits()
    fetchAllBenefits()
  }, [fetchCompanyBenefits, fetchAllBenefits])

  const handleAddBenefit = async (benefitId: string, isVerified: boolean = false) => {
    try {
      const response = await fetch(`/api/admin/companies/${companyId}/benefits`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ benefitId, isVerified })
      })

      if (response.ok) {
        const result = await response.json()
        alert(result.message)
        fetchCompanyBenefits()
        setShowAddModal(false)
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to add benefit')
      }
    } catch (error) {
      console.error('Error adding benefit:', error)
      alert('Failed to add benefit')
    }
  }

  const handleRemoveBenefit = async (benefitId: string, benefitName: string) => {
    if (!confirm(`Remove "${benefitName}" from ${companyName}? This will also delete all verifications.`)) {
      return
    }

    try {
      const response = await fetch(`/api/admin/companies/${companyId}/benefits?benefitId=${benefitId}`, {
        method: 'DELETE'
      })

      if (response.ok) {
        const result = await response.json()
        alert(result.message)
        fetchCompanyBenefits()
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to remove benefit')
      }
    } catch (error) {
      console.error('Error removing benefit:', error)
      alert('Failed to remove benefit')
    }
  }

  const handleBulkVerify = async (action: 'verify' | 'unverify') => {
    if (selectedBenefits.length === 0) {
      alert('Please select benefits to update')
      return
    }

    const reason = prompt(`Reason for ${action}ing ${selectedBenefits.length} benefit(s):`)
    if (reason === null) {return}

    try {
      const response = await fetch(`/api/admin/companies/${companyId}/benefits/verify`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action,
          benefitIds: selectedBenefits,
          reason
        })
      })

      if (response.ok) {
        const result = await response.json()
        alert(result.message)
        fetchCompanyBenefits()
        setSelectedBenefits([])
      } else {
        const error = await response.json()
        alert(error.error || `Failed to ${action} benefits`)
      }
    } catch (error) {
      console.error(`Error ${action}ing benefits:`, error)
      alert(`Failed to ${action} benefits`)
    }
  }

  const handleToggleVerification = async (companyBenefitId: string, currentStatus: boolean, benefitName: string) => {
    const newStatus = !currentStatus
    const action = newStatus ? 'verify' : 'unverify'
    const reason = prompt(`Reason for ${action}ing "${benefitName}":`)
    if (reason === null) {return}

    try {
      const response = await fetch(`/api/admin/companies/${companyId}/benefits/verify`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          companyBenefitId,
          isVerified: newStatus,
          reason
        })
      })

      if (response.ok) {
        const result = await response.json()
        alert(result.message)
        fetchCompanyBenefits()
      } else {
        const error = await response.json()
        alert(error.error || 'Failed to update verification')
      }
    } catch (error) {
      console.error('Error updating verification:', error)
      alert('Failed to update verification')
    }
  }

  const filteredCompanyBenefits = companyBenefits.filter(benefit => {
    const matchesSearch = benefit.benefit_name.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesCategory = categoryFilter === 'all' || benefit.category === categoryFilter
    return matchesSearch && matchesCategory
  })

  const availableBenefits = allBenefits.filter(benefit =>
    !companyBenefits.some(cb => cb.benefit_id === benefit.id)
  )



  const categories = [...new Set(companyBenefits.map(b => b.category))].sort()

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-6xl w-full max-h-[90vh] overflow-hidden">
        <div className="p-4 sm:p-6 border-b border-gray-200">
          <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:justify-between sm:space-y-0">
            <h2 className="text-lg sm:text-xl font-semibold text-gray-900">
              Manage Benefits for {companyName}
            </h2>
            <Button variant="outline" onClick={onClose} className="w-full sm:w-auto">
              Close
            </Button>
          </div>
        </div>

        <div className="p-4 sm:p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* Controls */}
          <div className="flex flex-col space-y-4 lg:flex-row lg:items-center lg:justify-between lg:space-y-0 mb-6">
            <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search benefits..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-md text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent w-full sm:w-auto"
                />
              </div>

              <select
                value={categoryFilter}
                onChange={(e) => setCategoryFilter(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-gray-900 bg-white focus:ring-2 focus:ring-blue-500 focus:border-transparent w-full sm:w-auto"
              >
                <option value="all">All Categories</option>
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </option>
                ))}
              </select>
            </div>

            <div className="flex flex-col space-y-2 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-2">
              {selectedBenefits.length > 0 && (
                <>
                  <Button
                    size="sm"
                    onClick={() => handleBulkVerify('verify')}
                    className="bg-green-600 hover:bg-green-700 w-full sm:w-auto"
                  >
                    <ShieldCheck className="w-4 h-4 mr-1" />
                    <span className="hidden sm:inline">Verify ({selectedBenefits.length})</span>
                    <span className="sm:hidden">Verify</span>
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleBulkVerify('unverify')}
                    className="w-full sm:w-auto"
                  >
                    <Shield className="w-4 h-4 mr-1" />
                    <span className="hidden sm:inline">Unverify ({selectedBenefits.length})</span>
                    <span className="sm:hidden">Unverify</span>
                  </Button>
                </>
              )}

              <Button
                size="sm"
                onClick={() => setShowAddModal(true)}
                className="bg-blue-600 hover:bg-blue-700 w-full sm:w-auto"
              >
                <Plus className="w-4 h-4 mr-1" />
                Add Benefit
              </Button>
            </div>
          </div>

          {/* Benefits List */}
          {loading ? (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
              <p className="text-gray-600 mt-2">Loading benefits...</p>
            </div>
          ) : filteredCompanyBenefits.length === 0 ? (
            <div className="text-center py-8">
              <AlertTriangle className="w-12 h-12 text-gray-500 mx-auto mb-4" />
              <p className="text-gray-600">No benefits found.</p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredCompanyBenefits.map((benefit) => (
                <div
                  key={benefit.company_benefit_id}
                  className={`p-4 border rounded-lg ${
                    benefit.is_verified ? 'bg-green-50 border-green-200' : 'bg-gray-50 border-gray-200'
                  }`}
                >
                  <div className="flex flex-col space-y-3 lg:flex-row lg:items-center lg:justify-between lg:space-y-0">
                    <div className="flex items-center space-x-3 flex-1 min-w-0">
                      <input
                        type="checkbox"
                        checked={selectedBenefits.includes(benefit.benefit_id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedBenefits([...selectedBenefits, benefit.benefit_id])
                          } else {
                            setSelectedBenefits(selectedBenefits.filter(id => id !== benefit.benefit_id))
                          }
                        }}
                        className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500 flex-shrink-0"
                      />

                      {benefit.icon && (
                        <span className="text-xl sm:text-2xl flex-shrink-0">{benefit.icon}</span>
                      )}

                      <div className="flex-1 min-w-0">
                        <h4 className="font-medium text-gray-900 truncate">{benefit.benefit_name}</h4>
                        <p className="text-sm text-gray-600 truncate">
                          {benefit.category?.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) || 'Other'}
                        </p>
                      </div>
                    </div>

                    <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4 lg:flex-shrink-0">
                      {/* Verification Counts */}
                      <div className="flex items-center space-x-2 text-sm">
                        <div className="flex items-center space-x-1">
                          <Users className="w-4 h-4 text-gray-500" />
                          <span className="text-green-600">{benefit.confirmed_count}</span>
                          <span className="text-gray-500">/</span>
                          <span className="text-red-600">{benefit.disputed_count}</span>
                        </div>
                      </div>

                      {/* Status */}
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                        benefit.is_verified
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {benefit.is_verified ? (
                          <>
                            <CheckCircle className="w-3 h-3 mr-1" />
                            Verified
                          </>
                        ) : (
                          <>
                            <XCircle className="w-3 h-3 mr-1" />
                            Unverified
                          </>
                        )}
                      </span>

                      {/* Actions */}
                      <div className="flex space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleToggleVerification(
                            benefit.company_benefit_id,
                            benefit.is_verified,
                            benefit.benefit_name
                          )}
                          className={`${benefit.is_verified ? 'text-yellow-600' : 'text-green-600'} flex-1 sm:flex-none`}
                        >
                          <span className="hidden sm:inline">{benefit.is_verified ? 'Unverify' : 'Verify'}</span>
                          <span className="sm:hidden">{benefit.is_verified ? 'Unv' : 'Ver'}</span>
                        </Button>

                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => window.open(`/companies/${companyId}`, '_blank')}
                          title="View on Company Page"
                          className="flex-shrink-0"
                        >
                          <Eye className="w-3 h-3" />
                        </Button>

                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleRemoveBenefit(benefit.benefit_id, benefit.benefit_name)}
                          className="flex-shrink-0"
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Add Benefit Modal */}
        {showAddModal && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60">
            <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-hidden">
              <div className="p-6 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">Add Benefit to {companyName}</h3>
              </div>

              <div className="p-6 overflow-y-auto max-h-[60vh]">

                {availableBenefits.length === 0 ? (
                  <p className="text-gray-600 text-center py-8">All benefits have been added to this company.</p>
                ) : (
                  <div className="space-y-3">
                    {availableBenefits.map((benefit) => (
                      <div key={benefit.id} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                        <div className="flex items-center space-x-3">
                          {benefit.icon && (
                            <span className="text-xl">{benefit.icon}</span>
                          )}
                          <div className="flex-1">
                            <h4 className="font-medium text-gray-900">{benefit.name}</h4>
                            <p className="text-sm text-gray-500">
                              {benefit.category?.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()) || 'Other'}
                            </p>
                          </div>
                        </div>
                        
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleAddBenefit(benefit.id, false)}
                          >
                            Add Unverified
                          </Button>
                          <Button
                            size="sm"
                            onClick={() => handleAddBenefit(benefit.id, true)}
                            className="bg-green-600 hover:bg-green-700"
                          >
                            Add Verified
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
              
              <div className="p-6 border-t border-gray-200">
                <Button variant="outline" onClick={() => setShowAddModal(false)}>
                  Close
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
